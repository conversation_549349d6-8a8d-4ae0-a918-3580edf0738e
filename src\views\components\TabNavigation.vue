<template>
    <div class="tab-navigation-container d-flex align-items-center">
        <v-tabs center-active fixed-tabs v-model="currentTab" class="flex-grow-1" style="color: #FFF;">
            <!-- <v-tab @click="openPage('inicio')">
                <div class="pb-1">
                    <v-icon>mdi-home</v-icon>
                </div>
            </v-tab> -->
            <v-tab @click="openPage('agenda')">
                <div class="pb-1">
                    <v-icon class="mr-3 d-none d-md-block">mdi-calendar-month</v-icon>
                </div>
                <!-- <i class="fas fa-calendar-alt mr-3 pt-1 d-none d-md-block"></i> -->
                {{ $t('mainNav.agenda') }}
            </v-tab>
            <v-tab @click="openPage('pacientes')" value="pacientes">
                <div class="pb-1">
                    <v-icon class="mr-3 d-none d-md-block">mdi-account-details</v-icon>
                </div>
                {{ $t('mainNav.patients') }}
            </v-tab>

            <v-tab @click="openPage('ortodontistas')" v-if="$user.system_admin">
                <div class="pb-1">
                    <v-icon class="mr-3 d-none d-md-block">mdi-doctor</v-icon>
                </div>
                {{ $t('mainNav.orthodontists') }}
            </v-tab>

            <v-tab @click="openPage('financeiro')">
                <div class="pb-1">
                    <v-icon class="mr-3 d-none d-md-block">mdi-currency-usd</v-icon>
                </div>
                {{ $t('mainNav.financial') }}
            </v-tab>


        </v-tabs>

        <!-- Componente de notificações -->
        <div class="notification-container ms-3">
            <notification-dropdown />
        </div>
    </div>
</template>

<script>

var tab = 0
import { useRoute } from 'vue-router';
import NotificationDropdown from '@/components/NotificationDropdown.vue';

export default {
    name: "tab-navigation",
    components: {
        NotificationDropdown
    },
    data() {
        return {
            tab,
            currentTab: 0
        }
    },
    methods: {
        openPage(page) {
            this.$router.push(`/${page}`)
        },

        updateCurrentTab() {
            let currentPathObject = this.$router.currentRoute.value.path.replace('/', '').split('/')[0]

            var tabsMap = {
                '': 0,
                'agenda': 0,
                'paciente': 1,
                'pacientes': 1,
                'ortodontista': 2,
                'ortodontistas': 2,
                'financeiro': 3,
            }

            this.currentTab = tabsMap[currentPathObject] || 0;
        }
    },
    mounted() {
        // Definir aba inicial baseada na rota atual
        this.updateCurrentTab();
    },
    watch: {
        // Observar mudanças na rota para atualizar a aba ativa
        '$route'() {
            this.updateCurrentTab();
        }
    },
    computed: {
        route: () => useRoute()
    }
}
</script>

<style scoped>
.tab-navigation-container {
    background-color: var(--v-theme-deep-purple-darken-4);
}

.notification-container {
    /* padding-right: 16px; */
}

/* Garantir que o dropdown apareça acima de outros elementos */
.notification-container :deep(.notification-dropdown) {
    z-index: 1060;
}
</style>