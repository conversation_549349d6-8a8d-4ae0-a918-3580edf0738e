<template>
  <div class="paciente-financeiro">
    <!-- Resumo Financeiro -->
    <div class="row mb-4">
      <div class="col-md-3">
        <div class="stats-card">
          <div class="stats-title">
            <i class="fas fa-check-circle me-1"></i>
            Total Pago
          </div>
          <div class="stats-value text-success">{{ formatCurrency(totalPago) }}</div>
        </div>
      </div>
      <div class="col-md-3">
        <div class="stats-card">
          <div class="stats-title">
            <i class="fas fa-clock me-1"></i>
            Total Pendente
          </div>
          <div class="stats-value text-warning">{{ formatCurrency(totalPendente) }}</div>
        </div>
      </div>
      <div class="col-md-3">
        <div class="stats-card">
          <div class="stats-title">
            <i class="fas fa-exclamation-triangle me-1"></i>
            Total Vencido
          </div>
          <div class="stats-value text-danger">{{ formatCurrency(totalVencido) }}</div>
        </div>
      </div>
      <div class="col-md-3">
        <div class="stats-card">
          <div class="stats-title">
            <i class="fas fa-calculator me-1"></i>
            Total Geral
          </div>
          <div class="stats-value text-info">{{ formatCurrency(totalGeral) }}</div>
        </div>
      </div>
    </div>

    <!-- Filtros -->
    <div class="row mb-4">
      <div class="col-12">
        <div class="card">
          <div class="card-body">
            <div class="row g-3">
              <div class="col-md-2">
                <label class="form-label">Status</label>
                <select class="form-select" v-model="filters.status" @change="applyFilters">
                  <option value="">Todos</option>
                  <option value="pendente">Pendente</option>
                  <option value="pago">Pago</option>
                  <option value="vencido">Vencido</option>
                  <option value="cancelado">Cancelado</option>
                </select>
              </div>
              <div class="col-md-3">
                <label class="form-label">Descrição</label>
                <input type="text" class="form-control" v-model="filters.descricao" @input="applyFilters" placeholder="Buscar por descrição...">
              </div>
              <div class="col-md-2">
                <label class="form-label">Data Início</label>
                <input type="date" class="form-control" v-model="filters.data_inicio" @change="applyFilters">
              </div>
              <div class="col-md-2">
                <label class="form-label">Data Fim</label>
                <input type="date" class="form-control" v-model="filters.data_fim" @change="applyFilters">
              </div>
              <div class="col-md-1 d-flex align-items-end">
                <button class="btn btn-outline-secondary btn-sm" @click="clearFilters" title="Limpar filtros">
                  <i class="fas fa-broom"></i>
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Botões de Ação -->
    <div class="row mb-3">
      <div class="col-12 d-flex gap-2 justify-content-end">
        <button class="btn btn-outline-secondary btn-sm" @click="$emit('generate-receipt')">
          <i class="fas fa-file-pdf me-1"></i>
          Gerar Recibo
        </button>
        <button class="btn btn-outline-primary btn-sm" @click="$emit('create-orcamento')">
          <i class="fas fa-calculator me-1"></i>
          Novo Orçamento
        </button>
        <button class="btn btn-primary btn-sm" @click="$emit('create')">
          <i class="fas fa-plus me-1"></i>
          Nova Fatura
        </button>
      </div>
    </div>

    <!-- Lista de Faturas -->
    <div class="row">
      <div class="col-12">
        <div class="card">
          <div class="card-header pb-0">
            <h6 class="mb-0">Faturas do Paciente</h6>
          </div>
          <div class="card-body px-0 pt-0 pb-2">
            <div class="table-responsive p-0">
              <table class="table align-items-center mb-0">
                <thead>
                  <tr>
                    <th class="text-uppercase text-secondary text-xxs font-weight-bolder opacity-7">
                      Descrição
                    </th>
                    <th class="text-center text-uppercase text-secondary text-xxs font-weight-bolder opacity-7">
                      Valor
                    </th>
                    <th class="text-center text-uppercase text-secondary text-xxs font-weight-bolder opacity-7">
                      Emissão
                    </th>
                    <th class="text-center text-uppercase text-secondary text-xxs font-weight-bolder opacity-7">
                      Vencimento
                    </th>
                    <th class="text-center text-uppercase text-secondary text-xxs font-weight-bolder opacity-7">
                      Status
                    </th>
                    <th class="text-center text-uppercase text-secondary text-xxs font-weight-bolder opacity-7">
                      Ações
                    </th>
                  </tr>
                </thead>
                <tbody>
                  <tr v-if="loading">
                    <td colspan="6" class="text-center py-4">
                      <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Carregando...</span>
                      </div>
                    </td>
                  </tr>
                  <tr v-else-if="filteredFaturas.length === 0">
                    <td colspan="6" class="text-center py-4 text-muted">
                      {{ faturas.length === 0 ? 'Nenhuma fatura encontrada para este paciente' : 'Nenhuma fatura corresponde aos filtros aplicados' }}
                    </td>
                  </tr>
                  <tr v-else v-for="fatura in filteredFaturas" :key="fatura.id">
                    <td>
                      <div class="d-flex px-2 py-1">
                        <div class="d-flex flex-column justify-content-center">
                          <h6 class="mb-0 text-sm">{{ fatura.descricao }}</h6>
                          <p class="text-xs text-secondary mb-0" v-if="fatura.observacoes">
                            {{ fatura.observacoes }}
                          </p>
                          <div v-if="fatura.parcelas_total > 1" class="text-xs text-info">
                            Parcela {{ fatura.parcela_numero }}/{{ fatura.parcelas_total }}
                          </div>
                        </div>
                      </div>
                    </td>
                    <td class="align-middle text-center text-sm">
                      <span class="font-weight-bold">{{ formatCurrency(fatura.valor_final) }}</span>
                      <div v-if="fatura.valor_nominal !== fatura.valor_final" class="text-xs text-secondary">
                        Original: {{ formatCurrency(fatura.valor_nominal) }}
                      </div>
                    </td>
                    <td class="align-middle text-center">
                      <span class="text-secondary text-xs font-weight-bold">
                        {{ formatDate(fatura.data_emissao) }}
                      </span>
                    </td>
                    <td class="align-middle text-center">
                      <span class="text-secondary text-xs font-weight-bold">
                        {{ formatDate(fatura.data_vencimento) }}
                      </span>
                      <div v-if="isOverdue(fatura)" class="text-xs text-danger">
                        Vencida há {{ getDaysOverdue(fatura) }} dias
                      </div>
                    </td>
                    <td class="align-middle text-center text-sm">
                      <span class="badge badge-sm" :class="getStatusBadgeClass(fatura.status)">
                        {{ getStatusText(fatura.status) }}
                      </span>
                      <div v-if="fatura.data_pagamento" class="text-xs text-secondary mt-1">
                        Pago em {{ formatDate(fatura.data_pagamento) }}
                      </div>
                    </td>
                    <td class="align-middle text-center">
                      <div class="dropdown">
                        <button class="btn btn-link text-secondary mb-0" data-bs-toggle="dropdown">
                          <i class="fas fa-ellipsis-v"></i>
                        </button>
                        <ul class="dropdown-menu">
                          <li>
                            <a class="dropdown-item" href="#" @click.prevent="$emit('edit', fatura)">
                              <i class="fas fa-edit me-2"></i>
                              Editar
                            </a>
                          </li>
                          <li v-if="fatura.status === 'pendente'">
                            <a class="dropdown-item" href="#" @click.prevent="markAsPaid(fatura)">
                              <i class="fas fa-check me-2"></i>
                              Marcar como Pago
                            </a>
                          </li>
                          <li>
                            <a class="dropdown-item" href="#" @click.prevent="$emit('generate-receipt', fatura)">
                              <i class="fas fa-file-pdf me-2"></i>
                              Gerar Recibo
                            </a>
                          </li>
                          <li>
                            <hr class="dropdown-divider">
                          </li>
                          <li>
                            <a class="dropdown-item text-danger" href="#" @click.prevent="$emit('delete', fatura.id)">
                              <i class="fas fa-trash me-2"></i>
                              Cancelar
                            </a>
                          </li>
                        </ul>
                      </div>
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { financeiroService } from '@/services/financeiroService';

export default {
  name: 'PacienteFinanceiro',
  props: {
    paciente: {
      type: Object,
      required: true
    },
    faturas: {
      type: Array,
      default: () => []
    },
    loading: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      filters: {
        status: '',
        descricao: '',
        data_inicio: '',
        data_fim: ''
      }
    };
  },
  computed: {
    filteredFaturas() {
      let filtered = [...this.faturas];

      if (this.filters.status) {
        filtered = filtered.filter(fatura => fatura.status === this.filters.status);
      }

      if (this.filters.descricao) {
        filtered = filtered.filter(fatura =>
          fatura.descricao.toLowerCase().includes(this.filters.descricao.toLowerCase())
        );
      }

      if (this.filters.data_inicio) {
        filtered = filtered.filter(fatura =>
          new Date(fatura.data_vencimento) >= new Date(this.filters.data_inicio)
        );
      }

      if (this.filters.data_fim) {
        filtered = filtered.filter(fatura =>
          new Date(fatura.data_vencimento) <= new Date(this.filters.data_fim)
        );
      }

      return filtered.sort((a, b) => new Date(b.data_vencimento) - new Date(a.data_vencimento));
    },

    totalPago() {
      return this.faturas
        .filter(f => f.status === 'pago')
        .reduce((sum, f) => sum + parseFloat(f.valor_final || 0), 0);
    },

    totalPendente() {
      return this.faturas
        .filter(f => f.status === 'pendente')
        .reduce((sum, f) => sum + parseFloat(f.valor_final || 0), 0);
    },

    totalVencido() {
      return this.faturas
        .filter(f => f.status === 'vencido' || this.isOverdue(f))
        .reduce((sum, f) => sum + parseFloat(f.valor_final || 0), 0);
    },

    totalGeral() {
      return this.faturas
        .reduce((sum, f) => sum + parseFloat(f.valor_final || 0), 0);
    }
  },
  methods: {
    formatCurrency: financeiroService.formatCurrency,
    formatDate: financeiroService.formatDate,
    getStatusBadgeClass: financeiroService.getStatusBadgeClass,
    getStatusText: financeiroService.getStatusText,

    applyFilters() {
      // Os filtros são aplicados automaticamente via computed property
    },

    clearFilters() {
      this.filters = {
        status: '',
        descricao: '',
        data_inicio: '',
        data_fim: ''
      };
    },

    markAsPaid(fatura) {
      const paymentData = {
        data_pagamento: new Date().toISOString().split('T')[0],
        meio_pagamento: 'Não especificado'
      };
      this.$emit('mark-paid', fatura.id, paymentData);
    },

    isOverdue(fatura) {
      if (fatura.status !== 'pendente') return false;
      return new Date(fatura.data_vencimento) < new Date();
    },

    getDaysOverdue(fatura) {
      if (!this.isOverdue(fatura)) return 0;
      const today = new Date();
      const vencimento = new Date(fatura.data_vencimento);
      const diffTime = today - vencimento;
      return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    }
  }
};
</script>

<style scoped>
.paciente-financeiro .card {
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

/* Estilos para os cards de estatísticas */
.stats-card {
  background-color: #fff;
  border-radius: 8px;
  padding: 0.8rem;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.05);
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
  border: 1px solid rgba(0, 0, 0, 0.05);
}

.stats-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
}

.stats-title {
  font-size: 0.75rem;
  font-weight: 600;
  color: #344767;
  text-transform: uppercase;
  margin-bottom: 0.4rem;
  letter-spacing: 0.5px;
}

.stats-value {
  font-size: 1.1rem;
  font-weight: 700;
  margin-bottom: 0.25rem;
}

.badge-warning {
  background-color: #fb6340;
}

.badge-success {
  background-color: #2dce89;
}

.badge-danger {
  background-color: #f5365c;
}

.badge-secondary {
  background-color: #8898aa;
}

.text-info {
  color: #11cdef !important;
}
</style>
