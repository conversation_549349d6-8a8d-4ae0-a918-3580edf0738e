<template>
  <div class="modal fade" id="modalFinanceiroCreate" tabindex="-1" aria-labelledby="modalFinanceiroCreateLabel" aria-hidden="true">
    <div class="modal-dialog modal-xl modal-dialog-centered">
      <div class="modal-content elegant-modal">
        <div class="modal-header elegant-header">
          <div class="d-flex align-items-center">
            <div class="modal-icon">
              <i class="fas fa-file-invoice-dollar"></i>
            </div>
            <div>
              <h5 class="modal-title mb-0 text-white" id="modalFinanceiroCreateLabel">Nova Fatura</h5>
              <div class="text-white-50 modal-subtitle">Criar uma nova fatura para cobrança</div>
            </div>
          </div>
          <button type="button" class="btn-close btn-close-white elegant-close" data-bs-dismiss="modal" aria-label="Close">
            <i class="fas fa-times"></i>
          </button>
        </div>

        <div class="modal-body elegant-body">
          <!-- Resumo do Orçamento (se aplicável) -->
          <div v-if="orcamentoResumo" class="orcamento-resumo mb-4">
            <div class="resumo-header">
              <i class="fas fa-calculator me-2"></i>
              <span>Baseado no Orçamento: {{ orcamentoResumo.titulo }}</span>
            </div>
            <div class="resumo-content">
              <div class="row">
                <div class="col-md-8">
                  <div class="servicos-list">
                    <div v-for="item in orcamentoResumo.itens" :key="item.id" class="servico-item">
                      <span class="servico-nome">{{ item.nome }}</span>
                      <span class="servico-valor">{{ formatCurrency(item.valor_total) }}</span>
                    </div>
                  </div>
                </div>
                <div class="col-md-4 text-end">
                  <div class="total-orcamento">
                    <span class="total-label">Total:</span>
                    <span class="total-valor">{{ formatCurrency(orcamentoResumo.valor_total) }}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Formulário da Fatura -->
          <fatura-form-elegant
            :form="faturaForm"
            :errors="errors"
            :pacientes="pacientes"
            :dentistas="dentistas"
            :preselected-paciente="preselectedPaciente"
            :paciente-selecionado="pacienteSelecionado"
            @update-form="updateFaturaForm"
            @abrir-busca-paciente="abrirBuscaPaciente"
            @limpar-paciente="limparPaciente"
          />
        </div>

        <div class="modal-footer elegant-footer">
          <button type="button" class="btn btn-light" data-bs-dismiss="modal">
            <i class="fas fa-times me-2"></i>
            Cancelar
          </button>
          <button type="button" class="btn btn-primary elegant-btn-primary" @click="save" :disabled="saving">
            <span v-if="saving" class="spinner-border spinner-border-sm me-2" role="status"></span>
            <i v-else class="fas fa-save me-2"></i>
            {{ saving ? 'Criando Fatura...' : 'Criar Fatura' }}
          </button>
        </div>
      </div>
    </div>
  </div>

  <!-- Modal de Busca de Paciente -->
  <paciente-busca-modal
    ref="pacienteBuscaModal"
    @paciente-selecionado="onPacienteSelecionado"
  />
</template>

<script>
import { financeiroService } from '@/services/financeiroService';
import { openModal, closeModal } from '@/utils/modalHelper';
import { searchPacientes } from '@/services/pacientesService';
import { getDentistas } from '@/services/dentistasService';
import cSwal from '@/utils/cSwal.js';
import FaturaFormElegant from './FaturaFormElegant.vue';
import PacienteBuscaModal from '@/components/Global/PacienteBuscaModal.vue';

export default {
  name: 'FinanceiroCreateModal',
  components: {
    FaturaFormElegant,
    PacienteBuscaModal,
  },
  emits: ['saved'],
  data() {
    return {
      saving: false,
      preselectedPaciente: null,
      pacienteSelecionado: null,
      pacientes: [],
      dentistas: [],
      orcamentoResumo: null, // Para quando a fatura for baseada em um orçamento
      faturaForm: {
        paciente_id: '',
        dentista_id: '',
        descricao: '',
        valor_nominal: '',
        data_vencimento: '',
        parcelas_total: 1,
        percentual_desconto: '',
        valor_desconto: '',
        percentual_acrescimo: '',
        valor_acrescimo: '',
        observacoes: ''
      },
      errors: {}
    };
  },
  methods: {
    formatCurrency: financeiroService.formatCurrency,

    open(pacienteId = null, pacienteObj = null, orcamentoData = null) {
      this.preselectedPaciente = pacienteId;
      this.pacienteSelecionado = pacienteObj;
      this.orcamentoResumo = orcamentoData || null;
      this.resetForm();
      this.clearErrors();

      if (pacienteId) {
        this.faturaForm.paciente_id = pacienteObj?.id_ficha || pacienteId;
      }

      // Se baseado em orçamento, preencher dados
      if (orcamentoData) {
        this.faturaForm.descricao = orcamentoData.titulo || orcamentoData.descricao;
        this.faturaForm.valor_nominal = orcamentoData.valor_total;
      }

      openModal('modalFinanceiroCreate');
    },

    resetForm() {
      this.orcamentoResumo = null;
      this.faturaForm = {
        paciente_id: this.preselectedPaciente || '',
        dentista_id: '',
        descricao: '',
        valor_nominal: '',
        data_vencimento: '',
        parcelas_total: 1,
        percentual_desconto: '',
        valor_desconto: '',
        percentual_acrescimo: '',
        valor_acrescimo: '',
        observacoes: ''
      };
    },

    clearErrors() {
      this.errors = {};
    },

    updateFaturaForm(field, value) {
      this.faturaForm[field] = value;
    },

    async save() {
      if (!this.validateForm()) {
        return;
      }

      this.saving = true;
      try {
        await this.saveFatura();
        this.closeModal();
        this.$emit('saved');
      } catch (error) {
        console.error('Erro ao salvar:', error);

        if (error.response && error.response.status === 422) {
          this.errors = error.response.data.data || {};
        } else {
          cSwal.cError('Erro ao salvar fatura');
        }
      } finally {
        this.saving = false;
      }
    },

    async saveFatura() {
      await financeiroService.createFatura(this.faturaForm);
      cSwal.cSuccess('Fatura criada com sucesso');
    },

    validateForm() {
      const validation = financeiroService.validateFaturaData(this.faturaForm);
      this.errors = validation.errors;
      return validation.isValid;
    },

    closeModal() {
      closeModal('modalFinanceiroCreate');
    },

    abrirBuscaPaciente() {
      this.$refs.pacienteBuscaModal.open();
    },

    onPacienteSelecionado(paciente) {
      this.pacienteSelecionado = paciente;
      this.faturaForm.paciente_id = paciente.id_ficha || paciente.id;
    },

    limparPaciente() {
      this.pacienteSelecionado = null;
      this.faturaForm.paciente_id = '';
    },

    async loadPacientes() {
      try {
        const response = await searchPacientes();
        this.pacientes = response || [];
      } catch (error) {
        console.error('Erro ao carregar pacientes:', error);
        this.pacientes = [];
      }
    },

    async loadDentistas() {
      try {
        const response = await getDentistas();
        this.dentistas = response || [];
      } catch (error) {
        console.error('Erro ao carregar dentistas:', error);
        this.dentistas = [];
      }
    }
  },

  mounted() {
    this.loadPacientes();
    this.loadDentistas();
  }
};
</script>

<style scoped>
/* Modal Elegante */
.elegant-modal {
  border-radius: 20px;
  border: none;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
  overflow: hidden;
}

.elegant-header {
  background: linear-gradient(135deg, #2C82C9 0%, #1a73e8 100%);
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
}

.modal-icon {
  width: 40px;
  height: 40px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 0.75rem;
  font-size: 1.2rem;
}

.elegant-close {
  background: rgba(255, 255, 255, 0.2);
  border-radius: 8px;
  border: none;
  opacity: 1;
  color: white;
  font-size: 1rem;
  padding: 0.5rem;
  transition: all 0.3s ease;
  width: 35px;
  height: 35px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.elegant-close:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: scale(1.05);
  color: white;
}

.elegant-close:focus {
  box-shadow: 0 0 0 0.2rem rgba(255, 255, 255, 0.3);
  color: white;
}

.elegant-close i {
  color: #fff !important;
}

.modal-subtitle {
  font-size: 0.9rem;
  margin-top: 0.25rem;
  opacity: 0.9;
}

.elegant-body {
  padding: 2rem;
  background: #f8f9fa;
}

.elegant-footer {
  background: white;
  border: none;
  padding: 0.75rem 1.5rem;
  gap: 1rem;
}

.elegant-btn-primary {
  background: linear-gradient(135deg, #2C82C9 0%, #1a73e8 100%);
  border: none;
  border-radius: 10px;
  padding: 0.75rem 1.5rem;
  font-weight: 600;
  transition: all 0.3s ease;
}

.elegant-btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(44, 130, 201, 0.4);
}

/* Resumo do Orçamento */
.orcamento-resumo {
  background: white;
  border-radius: 15px;
  padding: 1.5rem;
  border: 1px solid #e9ecef;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
}

.resumo-header {
  font-weight: 600;
  color: #495057;
  margin-bottom: 1rem;
  font-size: 1.1rem;
}

.servicos-list {
  max-height: 120px;
  overflow-y: auto;
}

.servico-item {
  display: flex;
  justify-content: space-between;
  padding: 0.5rem 0;
  border-bottom: 1px solid #f1f3f4;
}

.servico-item:last-child {
  border-bottom: none;
}

.servico-nome {
  color: #495057;
  font-size: 0.9rem;
}

.servico-valor {
  color: #28a745;
  font-weight: 600;
  font-size: 0.9rem;
}

.total-orcamento {
  background: #f8f9fa;
  padding: 1rem;
  border-radius: 10px;
  border: 2px solid #e9ecef;
}

.total-label {
  display: block;
  color: #6c757d;
  font-size: 0.9rem;
  margin-bottom: 0.25rem;
}

.total-valor {
  display: block;
  color: #28a745;
  font-weight: 700;
  font-size: 1.2rem;
}

/* Responsividade */
@media (max-width: 768px) {
  .elegant-header {
    padding: 1rem;
  }

  .elegant-body {
    padding: 1rem;
  }

  .elegant-footer {
    padding: 1rem;
  }

  .modal-icon {
    width: 40px;
    height: 40px;
    font-size: 1.2rem;
  }
}
</style>
